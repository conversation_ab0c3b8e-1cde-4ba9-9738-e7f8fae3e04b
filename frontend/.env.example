# TaraDental Frontend Environment Variables
# Copy this file to .env and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Your Supabase project URL
VITE_SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co

# Your Supabase anon/public key
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# =============================================================================
# API CONFIGURATION
# =============================================================================
# Backend API base URL
VITE_API_BASE_URL=http://localhost:3001

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/disable voice features
VITE_ENABLE_VOICE_FEATURES=true

# Enable/disable analytics tracking
VITE_ENABLE_ANALYTICS=false

# Enable/disable debug mode
VITE_DEBUG_MODE=true

# =============================================================================
# AI SERVICES (Frontend-specific if needed)
# =============================================================================
# Note: AI API keys should typically be kept on the backend for security
# These are only needed if you have client-side AI features

# OpenAI API key (if needed for client-side features)
# VITE_OPENAI_API_KEY=your_openai_api_key_here

# ElevenLabs API key (if needed for client-side features)
# VITE_ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Environment mode
VITE_NODE_ENV=development

# Enable/disable mock data
VITE_USE_MOCK_DATA=false

# =============================================================================
# DEPLOYMENT SETTINGS
# =============================================================================
# Base URL for production deployment
# VITE_BASE_URL=https://your-domain.com

# CDN URL for assets (if using a CDN)
# VITE_CDN_URL=https://cdn.your-domain.com
