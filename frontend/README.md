# TaraDental Frontend

React TypeScript application for the TaraDental platform.

## Structure

- `src/components/` - Reusable UI components organized by feature
- `src/pages/` - Page-level components
- `src/hooks/` - Custom React hooks
- `src/services/` - API and external service calls
- `src/types/` - TypeScript type definitions
- `src/utils/` - Utility functions
- `src/store/` - Zustand state management

## Development

```bash
npm install
npm run dev
```

## Testing

```bash
npm run test
npm run test:coverage
```
