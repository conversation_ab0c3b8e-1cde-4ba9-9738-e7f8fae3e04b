# TaraDental - AI Dental Sales Training Platform

TaraDental is an AI-powered dental sales training platform designed to help dental professionals improve their patient communication and treatment acceptance rates through simulated patient interactions, structured coaching using the LAARC framework, and gamified learning experiences.

## 🚀 Features

- **Daily Lessons System**: Structured learning content with XP-based gamification
- **AI Patient Simulation**: Practice with realistic AI patients with varying personalities
- **Chat with Tara**: On-demand coaching and support from AI assistant
- **LAARC Framework Training**: Structured objection handling methodology
- **Voice Integration**: Practice with voice responses and AI-generated patient voices
- **Progress Analytics**: Track improvement and performance over time
- **Gamification**: XP system, streaks, and achievements to maintain engagement

## 🏗️ Architecture

TaraDental follows a **Layered Modular Monolith** architecture:

- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript
- **Database**: Supabase (PostgreSQL) with real-time features
- **AI Services**: OpenAI for chat, ElevenLabs for voice synthesis
- **Deployment**: Vercel for both frontend and backend

## 📁 Project Structure

```
taradental/
├── frontend/           # React TypeScript application
├── backend/            # Node.js TypeScript API
├── database/           # Schema and migrations
├── shared/             # Common types and utilities
├── tests/              # Test files
├── docs/               # Documentation
└── rules/              # System patterns and guidelines
```

## 🛠️ Development Setup

### Prerequisites

- Node.js 18+ and npm 9+
- Supabase account
- OpenAI API key
- ElevenLabs API key

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-org/taradental.git
cd taradental
```

2. Install dependencies:
```bash
npm run setup
```

3. Set up environment variables:
```bash
# Copy example files
cp frontend/.env.example frontend/.env
cp backend/.env.example backend/.env

# Edit the .env files with your API keys and configuration
```

4. Set up the database:
```bash
# Apply database schema (instructions in database/README.md)
```

5. Start development servers:
```bash
npm run dev
```

This will start:
- Frontend at `http://localhost:5173`
- Backend at `http://localhost:3000`

## 🧪 Testing

Run all tests:
```bash
npm test
```

Run specific test suites:
```bash
npm run test:frontend    # Frontend unit tests
npm run test:backend     # Backend unit tests
npm run test:e2e         # End-to-end tests
```

## 📚 Documentation

- [API Documentation](docs/api/)
- [Development Guide](docs/development/)
- [Deployment Guide](docs/deployment/)
- [System Architecture](docs/taradental_architecture.md)
- [Product Requirements](docs/taradental_prd.md)

## 🚀 Deployment

### Vercel Deployment

1. Connect your repository to Vercel
2. Configure environment variables in Vercel dashboard
3. Deploy with automatic builds on push to main branch

See [Deployment Guide](docs/deployment/) for detailed instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in this repository
- Contact the development team
- Check the documentation in the `docs/` directory

## 🎯 Roadmap

- [ ] Phase 1: Foundation & Authentication
- [ ] Phase 2: Core Features & Lessons
- [ ] Phase 3: AI Integration & Roleplay
- [ ] Phase 4: Advanced Features & Polish

See [Project Tasks](docs/taradental_tasks.md) for detailed development plan.
