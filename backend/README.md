# TaraDental Backend

Node.js TypeScript API server for the TaraDental platform.

## Structure

- `src/controllers/` - HTTP request handlers
- `src/services/` - Business logic layer
- `src/models/` - Data models and types
- `src/middleware/` - Express middleware functions
- `src/routes/` - API route definitions
- `src/utils/` - Utility functions
- `src/config/` - Configuration management

## Development

```bash
npm install
npm run dev
```

## Testing

```bash
npm run test
npm run test:integration
```
