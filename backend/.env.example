# TaraDental Backend Environment Variables
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Port for the backend server
PORT=3001

# Node environment (development, production, test)
NODE_ENV=development

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Your Supabase project URL
SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co

# Your Supabase anon/public key
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Your Supabase service role key (for admin operations)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh4ZmJ0eWVwaG10aGZudnlzdHp0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDEwODczOSwiZXhwIjoyMDY1Njg0NzM5fQ.ftRlDpohikiVn3mlRVJWASzqKJuipEaa5ovjyHS-n4E

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT secret for token signing (generate a strong random string)
JWT_SECRET=your_jwt_secret_here_make_it_long_and_random

# JWT expiration time
JWT_EXPIRES_IN=7d

# Session secret for express-session (if used)
SESSION_SECRET=your_session_secret_here

# =============================================================================
# AI SERVICES
# =============================================================================
# OpenAI API configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000

# ElevenLabs API configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=your_preferred_voice_id

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:5173,http://localhost:4173

# =============================================================================
# RATE LIMITING
# =============================================================================
# Rate limit window in minutes
RATE_LIMIT_WINDOW=15

# Maximum requests per window
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size for uploads (in bytes)
MAX_FILE_SIZE=10485760

# Allowed file types (comma-separated)
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,audio/mpeg,audio/wav

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable/disable request logging
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email service configuration (if using external email service)
# EMAIL_SERVICE_API_KEY=your_email_service_api_key
# EMAIL_FROM_ADDRESS=<EMAIL>

# Analytics service configuration (if using external analytics)
# ANALYTICS_API_KEY=your_analytics_api_key

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Enable/disable debug mode
DEBUG_MODE=true

# Enable/disable mock data
USE_MOCK_DATA=false

# Enable/disable API documentation
ENABLE_API_DOCS=true
