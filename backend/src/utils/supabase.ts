import { createClient } from '@supabase/supabase-js';

// Environment variables validation
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl) {
  throw new Error('Missing SUPABASE_URL environment variable');
}

if (!supabaseAnonKey) {
  throw new Error('Missing SUPABASE_ANON_KEY environment variable');
}

if (!supabaseServiceRoleKey) {
  throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable');
}

// Create Supabase client for regular operations (respects RLS)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'taradental-backend',
    },
  },
});

// Create Supabase admin client (bypasses RLS)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'taradental-backend-admin',
    },
  },
});

// Export types for TypeScript support
export type { User, Session, AuthError } from '@supabase/supabase-js';

// Helper function to verify JWT token
export const verifyToken = async (token: string) => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) {
      console.error('Error verifying token:', error);
      return null;
    }
    return user;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
};

// Helper function to get user by ID (admin operation)
export const getUserById = async (userId: string) => {
  try {
    const { data, error } = await supabaseAdmin.auth.admin.getUserById(userId);
    if (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
    return data.user;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
};

// Helper function to create user (admin operation)
export const createUser = async (email: string, password: string, userData?: object) => {
  try {
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      user_metadata: userData,
      email_confirm: true,
    });
    
    if (error) {
      console.error('Error creating user:', error);
      throw error;
    }
    
    return data.user;
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

// Helper function to delete user (admin operation)
export const deleteUser = async (userId: string) => {
  try {
    const { error } = await supabaseAdmin.auth.admin.deleteUser(userId);
    if (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

// Helper function to update user metadata (admin operation)
export const updateUserMetadata = async (userId: string, metadata: object) => {
  try {
    const { data, error } = await supabaseAdmin.auth.admin.updateUserById(userId, {
      user_metadata: metadata,
    });
    
    if (error) {
      console.error('Error updating user metadata:', error);
      throw error;
    }
    
    return data.user;
  } catch (error) {
    console.error('Error updating user metadata:', error);
    throw error;
  }
};

// Helper function to list users (admin operation)
export const listUsers = async (page = 1, perPage = 50) => {
  try {
    const { data, error } = await supabaseAdmin.auth.admin.listUsers({
      page,
      perPage,
    });
    
    if (error) {
      console.error('Error listing users:', error);
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error listing users:', error);
    throw error;
  }
};

// Export default clients
export default { supabase, supabaseAdmin };
