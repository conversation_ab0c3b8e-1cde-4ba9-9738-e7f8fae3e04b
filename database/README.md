# TaraDental Database

Database schema and migrations for the TaraDental platform using Supabase.

## Structure

- `schema.sql` - Complete database schema
- `migrations/` - Database migration files
- `seed/` - Seed data for development

## Setup

1. Create a new Supabase project
2. Apply the schema: `psql -f schema.sql`
3. Run migrations in order
4. Seed development data

## Tables

- `users` - User accounts and progress tracking
- `daily_lessons` - Lesson content and structure
- `lesson_progress` - User lesson completion tracking
- `chat_sessions` - Chat with Tara sessions
- `chat_messages` - Individual chat messages
- `patient_personas` - AI patient personalities
- `objection_scenarios` - Roleplay scenarios
- `roleplay_sessions` - User roleplay attempts
- `xp_transactions` - XP earning history
- `user_streaks` - Daily activity streaks
