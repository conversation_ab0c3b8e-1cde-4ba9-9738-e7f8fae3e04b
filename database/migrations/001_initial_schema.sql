-- Migration 001: Initial Schema
-- Created: 2025-01-17
-- Description: Creates the complete initial database schema for TaraDental

-- =============================================================================
-- MIGRATION METADATA
-- =============================================================================

-- Create migrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS schema_migrations (
    version TEXT PRIMARY KEY,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    description TEXT
);

-- Record this migration
INSERT INTO schema_migrations (version, description) 
VALUES ('001', 'Initial schema creation with all core tables')
ON CONFLICT (version) DO NOTHING;

-- =============================================================================
-- EXTENSIONS
-- =============================================================================

-- Enable UUID extension for generating UUIDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable pgcrypto for password hashing and other crypto functions
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================================================
-- CUSTOM TYPES
-- =============================================================================

-- Enum for lesson types
DO $$ BEGIN
    CREATE TYPE lesson_type AS ENUM ('video', 'quiz', 'challenge', 'reading');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Enum for lesson difficulty levels
DO $$ BEGIN
    CREATE TYPE difficulty_level AS ENUM ('beginner', 'intermediate', 'advanced');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Enum for roleplay session status
DO $$ BEGIN
    CREATE TYPE session_status AS ENUM ('active', 'completed', 'abandoned');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Enum for message types in chat
DO $$ BEGIN
    CREATE TYPE message_type AS ENUM ('user', 'assistant', 'system');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Enum for XP transaction types
DO $$ BEGIN
    CREATE TYPE xp_transaction_type AS ENUM ('lesson_complete', 'quiz_pass', 'challenge_complete', 'streak_bonus', 'roleplay_complete', 'daily_login');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- =============================================================================
-- CORE TABLES
-- =============================================================================

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    
    -- Progress tracking
    total_xp INTEGER DEFAULT 0 NOT NULL,
    current_level INTEGER DEFAULT 1 NOT NULL,
    current_streak INTEGER DEFAULT 0 NOT NULL,
    longest_streak INTEGER DEFAULT 0 NOT NULL,
    
    -- Preferences
    preferred_voice_id TEXT,
    notification_preferences JSONB DEFAULT '{"email": true, "push": false}'::jsonb,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT users_total_xp_positive CHECK (total_xp >= 0),
    CONSTRAINT users_current_level_positive CHECK (current_level >= 1),
    CONSTRAINT users_current_streak_positive CHECK (current_streak >= 0),
    CONSTRAINT users_longest_streak_positive CHECK (longest_streak >= 0)
);

-- Daily lessons table
CREATE TABLE IF NOT EXISTS daily_lessons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    lesson_type lesson_type NOT NULL,
    difficulty difficulty_level DEFAULT 'beginner' NOT NULL,
    
    -- Ordering and scheduling
    order_index INTEGER NOT NULL,
    estimated_duration_minutes INTEGER DEFAULT 15,
    
    -- XP rewards
    xp_reward INTEGER DEFAULT 10 NOT NULL,
    
    -- Prerequisites
    prerequisite_lesson_ids UUID[],
    
    -- Metadata
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT daily_lessons_order_index_positive CHECK (order_index >= 0),
    CONSTRAINT daily_lessons_duration_positive CHECK (estimated_duration_minutes > 0),
    CONSTRAINT daily_lessons_xp_positive CHECK (xp_reward >= 0)
);

-- User lesson progress tracking
CREATE TABLE IF NOT EXISTS lesson_progress (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    lesson_id UUID NOT NULL REFERENCES daily_lessons(id) ON DELETE CASCADE,
    
    -- Progress tracking
    is_completed BOOLEAN DEFAULT false NOT NULL,
    completion_percentage INTEGER DEFAULT 0 NOT NULL,
    score INTEGER,
    time_spent_minutes INTEGER DEFAULT 0 NOT NULL,
    
    -- Attempts tracking
    attempt_count INTEGER DEFAULT 0 NOT NULL,
    
    -- Timestamps
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    last_accessed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    UNIQUE(user_id, lesson_id),
    CONSTRAINT lesson_progress_completion_percentage_valid CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    CONSTRAINT lesson_progress_score_valid CHECK (score IS NULL OR (score >= 0 AND score <= 100)),
    CONSTRAINT lesson_progress_time_spent_positive CHECK (time_spent_minutes >= 0),
    CONSTRAINT lesson_progress_attempt_count_positive CHECK (attempt_count >= 0)
);

-- Chat sessions with Tara
CREATE TABLE IF NOT EXISTS chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Session metadata
    title TEXT,
    context JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Individual chat messages
CREATE TABLE IF NOT EXISTS chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    
    -- Message content
    message_type message_type NOT NULL,
    content TEXT NOT NULL,
    metadata JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- AI patient personas for roleplay
CREATE TABLE IF NOT EXISTS patient_personas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Persona details
    name TEXT NOT NULL,
    age INTEGER,
    background TEXT,
    personality_traits JSONB NOT NULL,
    medical_history JSONB,
    
    -- Behavior settings
    objection_likelihood INTEGER DEFAULT 50 NOT NULL,
    difficulty difficulty_level DEFAULT 'beginner' NOT NULL,
    
    -- Voice settings
    voice_id TEXT,
    voice_settings JSONB,
    
    -- Metadata
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT patient_personas_age_valid CHECK (age IS NULL OR (age >= 0 AND age <= 120)),
    CONSTRAINT patient_personas_objection_likelihood_valid CHECK (objection_likelihood >= 0 AND objection_likelihood <= 100)
);

-- Objection handling scenarios
CREATE TABLE IF NOT EXISTS objection_scenarios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Scenario details
    title TEXT NOT NULL,
    description TEXT,
    initial_objection TEXT NOT NULL,
    context JSONB,
    
    -- Difficulty and categorization
    difficulty difficulty_level DEFAULT 'beginner' NOT NULL,
    category TEXT,
    
    -- LAARC framework guidance
    laarc_guidance JSONB,
    
    -- Metadata
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- User roleplay session attempts
CREATE TABLE IF NOT EXISTS roleplay_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    persona_id UUID NOT NULL REFERENCES patient_personas(id) ON DELETE CASCADE,
    scenario_id UUID REFERENCES objection_scenarios(id) ON DELETE SET NULL,
    
    -- Session details
    status session_status DEFAULT 'active' NOT NULL,
    conversation_log JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Performance metrics
    laarc_score JSONB,
    overall_score INTEGER,
    feedback TEXT,
    
    -- Timing
    duration_minutes INTEGER,
    
    -- Timestamps
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT roleplay_sessions_overall_score_valid CHECK (overall_score IS NULL OR (overall_score >= 0 AND overall_score <= 100)),
    CONSTRAINT roleplay_sessions_duration_positive CHECK (duration_minutes IS NULL OR duration_minutes >= 0)
);

-- XP transaction history
CREATE TABLE IF NOT EXISTS xp_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Transaction details
    transaction_type xp_transaction_type NOT NULL,
    amount INTEGER NOT NULL,
    description TEXT,
    
    -- Related entities
    related_lesson_id UUID REFERENCES daily_lessons(id) ON DELETE SET NULL,
    related_session_id UUID REFERENCES roleplay_sessions(id) ON DELETE SET NULL,
    
    -- Metadata
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    CONSTRAINT xp_transactions_amount_not_zero CHECK (amount != 0)
);

-- User streak tracking
CREATE TABLE IF NOT EXISTS user_streaks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Streak details
    streak_date DATE NOT NULL,
    activities_completed INTEGER DEFAULT 0 NOT NULL,
    xp_earned INTEGER DEFAULT 0 NOT NULL,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- Constraints
    UNIQUE(user_id, streak_date),
    CONSTRAINT user_streaks_activities_positive CHECK (activities_completed >= 0),
    CONSTRAINT user_streaks_xp_positive CHECK (xp_earned >= 0)
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_total_xp ON users(total_xp DESC);
CREATE INDEX IF NOT EXISTS idx_users_current_level ON users(current_level);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_at);

-- Daily lessons indexes
CREATE INDEX IF NOT EXISTS idx_daily_lessons_order ON daily_lessons(order_index);
CREATE INDEX IF NOT EXISTS idx_daily_lessons_type ON daily_lessons(lesson_type);
CREATE INDEX IF NOT EXISTS idx_daily_lessons_difficulty ON daily_lessons(difficulty);
CREATE INDEX IF NOT EXISTS idx_daily_lessons_active ON daily_lessons(is_active);

-- Lesson progress indexes
CREATE INDEX IF NOT EXISTS idx_lesson_progress_user ON lesson_progress(user_id);
CREATE INDEX IF NOT EXISTS idx_lesson_progress_lesson ON lesson_progress(lesson_id);
CREATE INDEX IF NOT EXISTS idx_lesson_progress_completed ON lesson_progress(is_completed);
CREATE INDEX IF NOT EXISTS idx_lesson_progress_completion_date ON lesson_progress(completed_at);

-- Chat sessions indexes
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_updated ON chat_sessions(updated_at DESC);

-- Chat messages indexes
CREATE INDEX IF NOT EXISTS idx_chat_messages_session ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created ON chat_messages(created_at);

-- Patient personas indexes
CREATE INDEX IF NOT EXISTS idx_patient_personas_difficulty ON patient_personas(difficulty);
CREATE INDEX IF NOT EXISTS idx_patient_personas_active ON patient_personas(is_active);

-- Objection scenarios indexes
CREATE INDEX IF NOT EXISTS idx_objection_scenarios_difficulty ON objection_scenarios(difficulty);
CREATE INDEX IF NOT EXISTS idx_objection_scenarios_category ON objection_scenarios(category);
CREATE INDEX IF NOT EXISTS idx_objection_scenarios_active ON objection_scenarios(is_active);

-- Roleplay sessions indexes
CREATE INDEX IF NOT EXISTS idx_roleplay_sessions_user ON roleplay_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_roleplay_sessions_persona ON roleplay_sessions(persona_id);
CREATE INDEX IF NOT EXISTS idx_roleplay_sessions_status ON roleplay_sessions(status);
CREATE INDEX IF NOT EXISTS idx_roleplay_sessions_started ON roleplay_sessions(started_at DESC);

-- XP transactions indexes
CREATE INDEX IF NOT EXISTS idx_xp_transactions_user ON xp_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_xp_transactions_type ON xp_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_xp_transactions_created ON xp_transactions(created_at DESC);

-- User streaks indexes
CREATE INDEX IF NOT EXISTS idx_user_streaks_user ON user_streaks(user_id);
CREATE INDEX IF NOT EXISTS idx_user_streaks_date ON user_streaks(streak_date DESC);

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_daily_lessons_updated_at ON daily_lessons;
CREATE TRIGGER update_daily_lessons_updated_at BEFORE UPDATE ON daily_lessons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_chat_sessions_updated_at ON chat_sessions;
CREATE TRIGGER update_chat_sessions_updated_at BEFORE UPDATE ON chat_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_patient_personas_updated_at ON patient_personas;
CREATE TRIGGER update_patient_personas_updated_at BEFORE UPDATE ON patient_personas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_objection_scenarios_updated_at ON objection_scenarios;
CREATE TRIGGER update_objection_scenarios_updated_at BEFORE UPDATE ON objection_scenarios FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- MIGRATION COMPLETION
-- =============================================================================

-- Update migration record
UPDATE schema_migrations 
SET applied_at = NOW() 
WHERE version = '001';
