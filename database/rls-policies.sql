-- TaraDental Row Level Security (RLS) Policies
-- This file contains all RLS policies for data security
-- Execute this after creating the schema to enable proper data access control

-- =============================================================================
-- ENABLE RLS ON ALL TABLES
-- =============================================================================

ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE lesson_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE patient_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE objection_scenarios ENABLE ROW LEVEL SECURITY;
ALTER TABLE roleplay_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE xp_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_streaks ENABLE ROW LEVEL SECURITY;

-- =============================================================================
-- USERS TABLE POLICIES
-- =============================================================================

-- Users can view their own profile
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Users can insert their own profile (for registration)
CREATE POLICY "Users can insert own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- =============================================================================
-- DAILY LESSONS TABLE POLICIES
-- =============================================================================

-- All authenticated users can view active lessons
CREATE POLICY "Authenticated users can view active lessons" ON daily_lessons
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

-- Only service role can modify lessons (admin operations)
CREATE POLICY "Service role can manage lessons" ON daily_lessons
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- LESSON PROGRESS TABLE POLICIES
-- =============================================================================

-- Users can view their own lesson progress
CREATE POLICY "Users can view own lesson progress" ON lesson_progress
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own lesson progress
CREATE POLICY "Users can insert own lesson progress" ON lesson_progress
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own lesson progress
CREATE POLICY "Users can update own lesson progress" ON lesson_progress
    FOR UPDATE USING (auth.uid() = user_id);

-- =============================================================================
-- CHAT SESSIONS TABLE POLICIES
-- =============================================================================

-- Users can view their own chat sessions
CREATE POLICY "Users can view own chat sessions" ON chat_sessions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own chat sessions
CREATE POLICY "Users can create own chat sessions" ON chat_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own chat sessions
CREATE POLICY "Users can update own chat sessions" ON chat_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own chat sessions
CREATE POLICY "Users can delete own chat sessions" ON chat_sessions
    FOR DELETE USING (auth.uid() = user_id);

-- =============================================================================
-- CHAT MESSAGES TABLE POLICIES
-- =============================================================================

-- Users can view messages from their own chat sessions
CREATE POLICY "Users can view own chat messages" ON chat_messages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM chat_sessions 
            WHERE chat_sessions.id = chat_messages.session_id 
            AND chat_sessions.user_id = auth.uid()
        )
    );

-- Users can insert messages to their own chat sessions
CREATE POLICY "Users can insert own chat messages" ON chat_messages
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM chat_sessions 
            WHERE chat_sessions.id = chat_messages.session_id 
            AND chat_sessions.user_id = auth.uid()
        )
    );

-- =============================================================================
-- PATIENT PERSONAS TABLE POLICIES
-- =============================================================================

-- All authenticated users can view active patient personas
CREATE POLICY "Authenticated users can view active personas" ON patient_personas
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

-- Only service role can manage patient personas
CREATE POLICY "Service role can manage personas" ON patient_personas
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- OBJECTION SCENARIOS TABLE POLICIES
-- =============================================================================

-- All authenticated users can view active objection scenarios
CREATE POLICY "Authenticated users can view active scenarios" ON objection_scenarios
    FOR SELECT USING (auth.role() = 'authenticated' AND is_active = true);

-- Only service role can manage objection scenarios
CREATE POLICY "Service role can manage scenarios" ON objection_scenarios
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- ROLEPLAY SESSIONS TABLE POLICIES
-- =============================================================================

-- Users can view their own roleplay sessions
CREATE POLICY "Users can view own roleplay sessions" ON roleplay_sessions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can create their own roleplay sessions
CREATE POLICY "Users can create own roleplay sessions" ON roleplay_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own roleplay sessions
CREATE POLICY "Users can update own roleplay sessions" ON roleplay_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- =============================================================================
-- XP TRANSACTIONS TABLE POLICIES
-- =============================================================================

-- Users can view their own XP transactions
CREATE POLICY "Users can view own xp transactions" ON xp_transactions
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own XP transactions (for client-side XP tracking)
CREATE POLICY "Users can insert own xp transactions" ON xp_transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Service role can manage all XP transactions (for admin operations)
CREATE POLICY "Service role can manage xp transactions" ON xp_transactions
    FOR ALL USING (auth.role() = 'service_role');

-- =============================================================================
-- USER STREAKS TABLE POLICIES
-- =============================================================================

-- Users can view their own streak data
CREATE POLICY "Users can view own streaks" ON user_streaks
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own streak data
CREATE POLICY "Users can insert own streaks" ON user_streaks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own streak data
CREATE POLICY "Users can update own streaks" ON user_streaks
    FOR UPDATE USING (auth.uid() = user_id);

-- =============================================================================
-- ADDITIONAL SECURITY FUNCTIONS
-- =============================================================================

-- Function to check if user is admin (based on user metadata)
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(
        (auth.jwt() ->> 'user_metadata')::jsonb ->> 'is_admin',
        'false'
    )::boolean;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current user's level (for level-based access control)
CREATE OR REPLACE FUNCTION get_user_level()
RETURNS INTEGER AS $$
BEGIN
    RETURN COALESCE(
        (SELECT current_level FROM users WHERE id = auth.uid()),
        1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================================================
-- ADMIN POLICIES (for future admin features)
-- =============================================================================

-- Admin users can view all data (add these policies when admin features are needed)
-- CREATE POLICY "Admins can view all users" ON users
--     FOR SELECT USING (is_admin());

-- CREATE POLICY "Admins can view all lesson progress" ON lesson_progress
--     FOR SELECT USING (is_admin());

-- CREATE POLICY "Admins can view all roleplay sessions" ON roleplay_sessions
--     FOR SELECT USING (is_admin());

-- =============================================================================
-- PERFORMANCE OPTIMIZATION POLICIES
-- =============================================================================

-- Create partial indexes for better performance on filtered queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_sessions 
ON users(id) WHERE last_login_at > NOW() - INTERVAL '30 days';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_lesson_progress_recent 
ON lesson_progress(user_id, lesson_id) WHERE completed_at > NOW() - INTERVAL '7 days';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_chat_sessions_recent 
ON chat_sessions(user_id) WHERE updated_at > NOW() - INTERVAL '7 days';

-- =============================================================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================================================

COMMENT ON TABLE users IS 'User profiles and progress tracking data';
COMMENT ON TABLE daily_lessons IS 'Lesson content and structure';
COMMENT ON TABLE lesson_progress IS 'User progress through lessons';
COMMENT ON TABLE chat_sessions IS 'Chat sessions with Tara AI';
COMMENT ON TABLE chat_messages IS 'Individual messages in chat sessions';
COMMENT ON TABLE patient_personas IS 'AI patient personalities for roleplay';
COMMENT ON TABLE objection_scenarios IS 'Roleplay scenarios and objections';
COMMENT ON TABLE roleplay_sessions IS 'User roleplay session attempts';
COMMENT ON TABLE xp_transactions IS 'XP earning and spending history';
COMMENT ON TABLE user_streaks IS 'Daily activity streak tracking';

-- Add column comments for important fields
COMMENT ON COLUMN users.total_xp IS 'Total XP earned by the user';
COMMENT ON COLUMN users.current_level IS 'Current level based on XP';
COMMENT ON COLUMN users.current_streak IS 'Current consecutive days of activity';
COMMENT ON COLUMN daily_lessons.content IS 'JSON structure containing lesson content';
COMMENT ON COLUMN lesson_progress.completion_percentage IS 'Percentage of lesson completed (0-100)';
COMMENT ON COLUMN roleplay_sessions.laarc_score IS 'JSON object with scores for each LAARC component';
COMMENT ON COLUMN patient_personas.personality_traits IS 'JSON array of personality characteristics';
COMMENT ON COLUMN objection_scenarios.laarc_guidance IS 'JSON object with guidance for each LAARC step';
